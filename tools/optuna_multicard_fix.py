#!/usr/bin/env python3
"""
Optuna多卡环境修复工具
解决SQLite数据库锁定问题的多种方案
"""

import os
import sys
import argparse
import logging
from typing import Optional
import optuna

logger = logging.getLogger(__name__)


class OptunaMultiCardFix:
    """Optuna多卡环境修复工具"""
    
    def __init__(self):
        self.rank = int(os.environ.get('RANK', 0))
        self.world_size = int(os.environ.get('WORLD_SIZE', 1))
        self.local_rank = int(os.environ.get('LOCAL_RANK', 0))
        
    def get_storage_solution(self, storage_type: str = "auto") -> str:
        """
        获取存储解决方案
        
        Args:
            storage_type: 存储类型 ('auto', 'redis', 'memory', 'sqlite_safe')
            
        Returns:
            存储URL
        """
        if storage_type == "auto":
            # 自动选择最佳方案
            if self.world_size > 1:
                # 多卡环境：优先Redis，其次内存
                if self._check_redis_available():
                    return self._get_redis_storage()
                else:
                    logger.warning("⚠️ Redis不可用，使用内存存储（结果不会持久化）")
                    return "memory"
            else:
                # 单卡环境：使用SQLite
                return "sqlite:///work_dirs/optuna_studies.db"
                
        elif storage_type == "redis":
            return self._get_redis_storage()
            
        elif storage_type == "memory":
            return "memory"
            
        elif storage_type == "sqlite_safe":
            return "sqlite:///work_dirs/optuna_studies.db"
            
        else:
            raise ValueError(f"不支持的存储类型: {storage_type}")
    
    def _check_redis_available(self) -> bool:
        """检查Redis是否可用"""
        try:
            import redis
            r = redis.Redis(host='localhost', port=6379, db=0)
            r.ping()
            return True
        except:
            return False
    
    def _get_redis_storage(self) -> str:
        """获取Redis存储URL"""
        redis_host = os.environ.get('REDIS_HOST', 'localhost')
        redis_port = os.environ.get('REDIS_PORT', '6379')
        redis_db = os.environ.get('REDIS_DB', '0')
        
        return f"redis://{redis_host}:{redis_port}/{redis_db}"
    
    def create_study_with_fix(self, 
                             study_name: str,
                             storage_type: str = "auto",
                             direction: str = 'minimize') -> Optional[optuna.Study]:
        """
        使用修复方案创建study
        
        Args:
            study_name: study名称
            storage_type: 存储类型
            direction: 优化方向
            
        Returns:
            optuna Study对象
        """
        storage_url = self.get_storage_solution(storage_type)
        
        logger.info(f"🔧 进程{self.rank}/{self.world_size} 使用存储: {storage_url}")
        
        if storage_type == "sqlite_safe" or (storage_type == "auto" and storage_url.startswith("sqlite")):
            # 使用安全SQLite存储
            from tools.optuna_storage_manager import create_safe_optuna_study
            return create_safe_optuna_study(study_name, storage_url.replace("sqlite:///", ""), direction)
        else:
            # 使用标准存储
            return optuna.create_study(
                study_name=study_name,
                storage=storage_url if storage_url != "memory" else None,
                direction=direction,
                load_if_exists=True
            )
    
    def print_recommendations(self):
        """打印推荐方案"""
        print("=" * 60)
        print("🔧 Optuna多卡环境解决方案推荐")
        print("=" * 60)
        
        if self.world_size > 1:
            print(f"📊 检测到多卡环境: {self.world_size}卡")
            print("\n🎯 推荐方案（按优先级排序）:")
            print("1. Redis存储（生产环境推荐）")
            print("   - 启动Redis: docker run -d -p 6379:6379 redis")
            print("   - 使用参数: --storage redis://localhost:6379/0")
            print()
            print("2. 安全SQLite存储（开发环境推荐）")
            print("   - 自动处理进程锁")
            print("   - 使用参数: --storage sqlite:///work_dirs/optuna_studies.db")
            print()
            print("3. 内存存储（测试环境）")
            print("   - 不持久化结果")
            print("   - 使用参数: --storage memory")
        else:
            print("📊 检测到单卡环境")
            print("✅ 可以直接使用SQLite存储，无需特殊处理")
        
        print("\n🚨 避免的问题:")
        print("- ❌ 多进程同时访问SQLite导致数据库锁定")
        print("- ❌ 进程间study状态不同步")
        print("- ❌ 训练结果丢失")
        print("=" * 60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Optuna多卡环境修复工具')
    parser.add_argument('--action', 
                       choices=['check', 'recommend', 'test'],
                       default='recommend',
                       help='操作类型')
    parser.add_argument('--storage-type',
                       choices=['auto', 'redis', 'memory', 'sqlite_safe'],
                       default='auto',
                       help='存储类型')
    parser.add_argument('--study-name', 
                       default='test_multicard_fix',
                       help='测试study名称')
    
    args = parser.parse_args()
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建修复工具
    fix_tool = OptunaMultiCardFix()
    
    if args.action == 'check':
        print("🔍 检查当前环境...")
        print(f"进程信息: Rank {fix_tool.rank}/{fix_tool.world_size}")
        print(f"Redis可用: {'✅' if fix_tool._check_redis_available() else '❌'}")
        
    elif args.action == 'recommend':
        fix_tool.print_recommendations()
        
    elif args.action == 'test':
        print("🧪 测试存储方案...")
        try:
            study = fix_tool.create_study_with_fix(
                args.study_name, 
                args.storage_type
            )
            if study:
                print(f"✅ Study创建成功: {study.study_name}")
                print(f"存储类型: {type(study.storage).__name__}")
            else:
                print("⚠️ 非主进程，等待主进程创建study")
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")


if __name__ == "__main__":
    main()
