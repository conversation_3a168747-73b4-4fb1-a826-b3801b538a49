#!/usr/bin/env python3
"""
Optuna存储管理器 - 解决多进程SQLite锁定问题
"""

import os
import time
import fcntl
import sqlite3
import logging
from typing import Optional
from contextlib import contextmanager
import optuna
from optuna.storages import RDBStorage

logger = logging.getLogger(__name__)


class SafeSQLiteStorage:
    """
    安全的SQLite存储管理器
    解决多进程环境下的数据库锁定问题
    """
    
    def __init__(self, db_path: str, timeout: int = 30):
        """
        初始化安全SQLite存储
        
        Args:
            db_path: 数据库文件路径
            timeout: 锁等待超时时间（秒）
        """
        self.db_path = os.path.abspath(db_path)
        self.lock_path = f"{self.db_path}.lock"
        self.timeout = timeout
        
        # 确保目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
    @contextmanager
    def acquire_lock(self):
        """获取文件锁"""
        lock_file = None
        try:
            # 创建锁文件
            lock_file = open(self.lock_path, 'w')
            
            # 尝试获取排他锁
            start_time = time.time()
            while time.time() - start_time < self.timeout:
                try:
                    fcntl.flock(lock_file.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
                    logger.debug(f"获取数据库锁成功: {self.db_path}")
                    yield
                    return
                except IOError:
                    time.sleep(0.1)  # 等待100ms后重试
                    
            raise TimeoutError(f"获取数据库锁超时: {self.timeout}秒")
            
        finally:
            if lock_file:
                try:
                    fcntl.flock(lock_file.fileno(), fcntl.LOCK_UN)
                    lock_file.close()
                    # 清理锁文件
                    if os.path.exists(self.lock_path):
                        os.remove(self.lock_path)
                except:
                    pass
    
    def create_study_safe(self, study_name: str, direction: str = 'minimize') -> optuna.Study:
        """
        安全创建或加载study
        
        Args:
            study_name: study名称
            direction: 优化方向
            
        Returns:
            optuna Study对象
        """
        storage_url = f"sqlite:///{self.db_path}"
        
        with self.acquire_lock():
            try:
                # 初始化数据库（如果不存在）
                self._init_database()
                
                # 创建或加载study
                study = optuna.create_study(
                    study_name=study_name,
                    storage=storage_url,
                    direction=direction,
                    load_if_exists=True
                )
                
                logger.info(f"✅ Study安全创建/加载成功: {study_name}")
                return study
                
            except Exception as e:
                logger.error(f"❌ Study创建失败: {str(e)}")
                raise
    
    def _init_database(self):
        """初始化数据库"""
        if not os.path.exists(self.db_path):
            # 创建空数据库文件
            conn = sqlite3.connect(self.db_path)
            conn.close()
            logger.info(f"✅ 数据库文件创建成功: {self.db_path}")


class MultiProcessOptunaManager:
    """
    多进程Optuna管理器
    专门处理多卡训练环境下的Optuna使用
    """
    
    def __init__(self, storage_path: str = "work_dirs/optuna_studies.db"):
        """
        初始化多进程管理器
        
        Args:
            storage_path: 数据库存储路径
        """
        self.storage_path = storage_path
        self.safe_storage = SafeSQLiteStorage(storage_path)
        
        # 获取当前进程信息
        self.rank = int(os.environ.get('RANK', 0))
        self.world_size = int(os.environ.get('WORLD_SIZE', 1))
        self.local_rank = int(os.environ.get('LOCAL_RANK', 0))
        
        logger.info(f"🔧 多进程管理器初始化 - Rank: {self.rank}/{self.world_size}")
    
    def create_study(self, study_name: str, direction: str = 'minimize') -> Optional[optuna.Study]:
        """
        创建study（仅主进程执行）
        
        Args:
            study_name: study名称
            direction: 优化方向
            
        Returns:
            optuna Study对象（仅主进程返回，其他进程返回None）
        """
        if self.rank == 0:
            # 仅主进程创建study
            logger.info(f"🎯 主进程创建Study: {study_name}")
            return self.safe_storage.create_study_safe(study_name, direction)
        else:
            # 非主进程等待主进程完成
            logger.info(f"⏳ 进程{self.rank}等待主进程创建Study")
            self._wait_for_study_creation(study_name)
            return None
    
    def _wait_for_study_creation(self, study_name: str, max_wait: int = 60):
        """等待主进程创建study"""
        storage_url = f"sqlite:///{self.storage_path}"
        
        start_time = time.time()
        while time.time() - start_time < max_wait:
            try:
                # 尝试加载study
                study = optuna.load_study(
                    study_name=study_name,
                    storage=storage_url
                )
                logger.info(f"✅ 进程{self.rank}成功连接到Study: {study_name}")
                return study
            except:
                time.sleep(1)  # 等待1秒后重试
                
        raise TimeoutError(f"进程{self.rank}等待Study创建超时")
    
    def get_storage_url(self) -> str:
        """获取存储URL"""
        return f"sqlite:///{self.storage_path}"
    
    def is_main_process(self) -> bool:
        """判断是否为主进程"""
        return self.rank == 0


def create_safe_optuna_study(study_name: str, 
                           storage_path: str = "work_dirs/optuna_studies.db",
                           direction: str = 'minimize') -> optuna.Study:
    """
    安全创建Optuna study的便捷函数
    
    Args:
        study_name: study名称
        storage_path: 存储路径
        direction: 优化方向
        
    Returns:
        optuna Study对象
    """
    manager = MultiProcessOptunaManager(storage_path)
    
    if manager.is_main_process():
        # 主进程创建study
        return manager.create_study(study_name, direction)
    else:
        # 非主进程等待并连接
        manager._wait_for_study_creation(study_name)
        storage_url = manager.get_storage_url()
        return optuna.load_study(study_name=study_name, storage=storage_url)


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 创建安全的study
    study = create_safe_optuna_study("test_study")
    print(f"Study创建成功: {study.study_name}")
