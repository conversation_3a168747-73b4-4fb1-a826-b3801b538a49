#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Optuna训练管理器 - 集成optuna超参数优化到mmaction2训练流程
Created by: Moss
Date: 2025-08-23
"""

import os
import sys
import json
import argparse
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, Any, Optional, Union
import optuna
from optuna.trial import Trial
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from configs.recognition.Multimodal.optuna_templates.hyperparameter_mapping import (
    HYPERPARAMETER_MAPPING, PARAMETER_GROUPS, DEFAULT_OPTIMIZATION_GROUP,
    get_hyperparameters_by_group, get_all_hyperparameters
)
from tools.config_path_resolver import ConfigPathResolver, ConfigUpdater
from tools.optuna_storage_manager import MultiProcessOptunaManager, create_safe_optuna_study

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OptunaTrainer:
    """
    Optuna训练管理器
    
    功能：
    1. 作为optuna和mmaction2训练流程的桥梁
    2. 动态生成配置文件
    3. 管理训练进程
    4. 收集和返回优化目标值
    """
    
    def __init__(self,
                 base_config_path: str,
                 study_name: str = "mmaction2_hyperopt",
                 storage_url: Optional[str] = None,
                 work_dir_base: str = "./work_dirs/optuna_trials",
                 optimization_group: str = DEFAULT_OPTIMIZATION_GROUP,
                 launcher: str = 'none',
                 gpus: Optional[str] = None):
        """
        初始化Optuna训练管理器

        Args:
            base_config_path: 基础配置文件路径
            study_name: optuna study名称
            storage_url: optuna存储URL，None表示使用内存存储
            work_dir_base: 试验工作目录基础路径
            optimization_group: 优化参数组名称
            launcher: 分布式训练启动器 ('none', 'pytorch', 'slurm', 'mpi')
            gpus: GPU设备ID，如 '0,1,2,3' 或 'auto'
        """
        self.base_config_path = base_config_path
        self.study_name = study_name
        self.storage_url = storage_url
        self.work_dir_base = work_dir_base
        self.optimization_group = optimization_group
        self.launcher = launcher
        self.gpus = gpus

        # 确保工作目录存在
        os.makedirs(work_dir_base, exist_ok=True)

        # 验证基础配置文件存在
        if not os.path.exists(base_config_path):
            raise FileNotFoundError(f"Base config file not found: {base_config_path}")

        # 初始化超参数映射和配置更新器
        self.hyperparameter_mapping = get_hyperparameters_by_group(optimization_group)
        self.config_updater = ConfigUpdater(self.hyperparameter_mapping)
        self.config_resolver = ConfigPathResolver()

        logger.info(f"Initialized with optimization group: {optimization_group}")
        logger.info(f"Optimizing {len(self.hyperparameter_mapping)} hyperparameters")
    
    def suggest_hyperparameters(self, trial: Trial) -> Dict[str, Any]:
        """
        基于映射配置动态建议超参数值

        Args:
            trial: optuna trial对象

        Returns:
            超参数字典
        """
        hyperparams = {}

        for param_name, param_config in self.hyperparameter_mapping.items():
            suggest_type = param_config['type']
            suggest_params = param_config['params']

            # 根据类型调用相应的suggest方法
            if suggest_type == 'suggest_float':
                hyperparams[param_name] = trial.suggest_float(param_name, **suggest_params)
            elif suggest_type == 'suggest_int':
                hyperparams[param_name] = trial.suggest_int(param_name, **suggest_params)
            elif suggest_type == 'suggest_categorical':
                hyperparams[param_name] = trial.suggest_categorical(param_name, **suggest_params)
            else:
                logger.warning(f"Unknown suggest type: {suggest_type} for parameter: {param_name}")

        logger.info(f"Generated hyperparameters for {len(hyperparams)} parameters")
        return hyperparams
    
    def create_config(self, hyperparams: Dict[str, Any], trial_id: int, trial: Optional[Trial] = None) -> str:
        """
        基于超参数创建配置文件

        Args:
            hyperparams: 超参数字典
            trial_id: 试验ID

        Returns:
            生成的配置文件路径
        """
        # 读取基础配置文件
        with open(self.base_config_path, 'r', encoding='utf-8') as f:
            base_config_content = f.read()

        # 创建试验特定的配置内容
        trial_config_content = base_config_content
        
        # 生成基于映射的配置更新
        config_updates = self._generate_config_updates(hyperparams)
        
        # 工作目录配置
        work_dir = os.path.join(self.work_dir_base, f"trial_{trial_id}")
        work_dir_config = f"work_dir = '{work_dir}'"

        # Hook配置（集成OptunaHook with Early Pruning）
        # 使用环境变量传递trial信息，避免序列化问题
        hook_config = f"""
# Optuna Hook Configuration with Early Pruning
import os
import optuna

# 从环境变量获取trial信息
_trial_number = os.environ.get('OPTUNA_TRIAL_NUMBER', '{trial_id}')
_study_name = os.environ.get('OPTUNA_STUDY_NAME', '{self.study_name}')
_storage_url = os.environ.get('OPTUNA_STORAGE_URL', '{self.storage_url or ""}')

custom_hooks = [
    dict(
        type='OptunaHook',
        trial_number=int(_trial_number),
        study_name=_study_name,
        storage_url=_storage_url if _storage_url else None,
        monitor_metric='val/acc',
        pruning_enabled=True,
        min_epochs=10,
        patience=5
    )
]"""
"""

        # 组合完整配置
        trial_config_content += f"\n\n# Optuna Trial {trial_id} Configuration\n"
        trial_config_content += config_updates + "\n"
        trial_config_content += work_dir_config + "\n"
        trial_config_content += hook_config + "\n"
        
        # 保存配置文件
        config_path = os.path.join(self.work_dir_base, f"trial_{trial_id}_config.py")
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write(trial_config_content)

        logger.info(f"Created config file: {config_path}")
        return config_path

    def _generate_config_updates(self, hyperparams: Dict[str, Any]) -> str:
        """
        基于超参数映射生成配置更新代码

        Args:
            hyperparams: 超参数字典

        Returns:
            配置更新的Python代码字符串
        """
        config_updates = []

        # 按配置路径分组超参数
        path_groups = {}
        for param_name, param_value in hyperparams.items():
            if param_name not in self.hyperparameter_mapping:
                continue

            config_path = self.hyperparameter_mapping[param_name]['config_path']
            root_key = config_path.split('.')[0]

            if root_key not in path_groups:
                path_groups[root_key] = []
            path_groups[root_key].append((config_path, param_value, param_name))

        # 为每个根配置生成更新代码
        for root_key, params in path_groups.items():
            config_updates.append(f"# Update {root_key} configuration")

            if root_key == 'optim_wrapper':
                config_updates.append(self._generate_optimizer_config(params))
            elif root_key == 'model':
                config_updates.append(self._generate_model_config(params))
            elif root_key.endswith('_dataloader'):
                config_updates.append(self._generate_dataloader_config(params, root_key))
            elif root_key == 'param_scheduler':
                config_updates.append(self._generate_scheduler_config(params))
            else:
                # 通用配置更新
                config_updates.append(self._generate_generic_config(params, root_key))

            config_updates.append("")  # 添加空行

        return "\n".join(config_updates)

    def _generate_optimizer_config(self, params: list) -> str:
        """生成优化器配置"""
        optimizer_params = {}
        custom_keys = {}
        clip_grad_params = {}

        for config_path, param_value, param_name in params:
            path_parts = config_path.split('.')

            if 'optimizer' in path_parts:
                if path_parts[-1] in ['lr', 'weight_decay']:
                    optimizer_params[path_parts[-1]] = param_value
            elif 'custom_keys' in path_parts:
                module_name = path_parts[3]  # optim_wrapper.paramwise_cfg.custom_keys.{module_name}
                if module_name not in custom_keys:
                    custom_keys[module_name] = {}
                custom_keys[module_name][path_parts[-1]] = param_value
            elif 'clip_grad' in path_parts:
                clip_grad_params[path_parts[-1]] = param_value

        config_lines = ["optim_wrapper = dict("]
        config_lines.append("    type='OptimWrapper',")

        if optimizer_params:
            config_lines.append("    optimizer=dict(")
            config_lines.append("        type='AdamW',")
            config_lines.append("        betas=(0.9, 0.999),")
            for key, value in optimizer_params.items():
                config_lines.append(f"        {key}={value},")
            config_lines.append("    ),")

        if custom_keys:
            config_lines.append("    paramwise_cfg=dict(")
            config_lines.append("        custom_keys={")
            for module_name, module_params in custom_keys.items():
                param_str = ", ".join([f"{k}={v}" for k, v in module_params.items()])
                config_lines.append(f"            '{module_name}': dict({param_str}),")
            config_lines.append("        }")
            config_lines.append("    ),")

        if clip_grad_params:
            param_str = ", ".join([f"{k}={v}" for k, v in clip_grad_params.items()])
            config_lines.append(f"    clip_grad=dict({param_str})")

        config_lines.append(")")

        return "\n".join(config_lines)

    def _generate_model_config(self, params: list) -> str:
        """生成模型配置"""
        model_updates = {}

        for config_path, param_value, param_name in params:
            path_parts = config_path.split('.')[1:]  # 去掉'model'前缀

            # 构建嵌套字典
            current = model_updates
            for part in path_parts[:-1]:
                if part not in current:
                    current[part] = {}
                current = current[part]
            current[path_parts[-1]] = param_value

        def dict_to_config_str(d, indent=1):
            lines = []
            for key, value in d.items():
                if isinstance(value, dict):
                    lines.append(f"{'    ' * indent}{key}=dict(")
                    lines.extend(dict_to_config_str(value, indent + 1))
                    lines.append(f"{'    ' * indent}),")
                else:
                    lines.append(f"{'    ' * indent}{key}={value},")
            return lines

        config_lines = ["model.update(dict("]
        config_lines.extend(dict_to_config_str(model_updates))
        config_lines.append("))")

        return "\n".join(config_lines)

    def _generate_dataloader_config(self, params: list, dataloader_name: str) -> str:
        """生成数据加载器配置"""
        dataloader_params = {}

        for config_path, param_value, param_name in params:
            path_parts = config_path.split('.')
            param_key = path_parts[-1]
            dataloader_params[param_key] = param_value

        param_str = ", ".join([f"{k}={v}" for k, v in dataloader_params.items()])
        return f"{dataloader_name}.update(dict({param_str}))"

    def _generate_scheduler_config(self, params: list) -> str:
        """生成学习率调度器配置"""
        # 这里需要根据具体的调度器配置来实现
        # 暂时返回简单的配置
        scheduler_params = {}
        for config_path, param_value, param_name in params:
            scheduler_params[param_name] = param_value

        warmup_epochs = scheduler_params.get('warmup_epochs', 5)
        cosine_t_max = scheduler_params.get('cosine_t_max', 95)

        config_lines = [
            "param_scheduler = [",
            "    dict(",
            "        type='LinearLR',",
            "        start_factor=0.01,",
            "        by_epoch=True,",
            "        begin=0,",
            f"        end={warmup_epochs}",
            "    ),",
            "    dict(",
            "        type='CosineAnnealingLR',",
            f"        T_max={cosine_t_max},",
            "        eta_min=1e-6,",
            "        by_epoch=True,",
            f"        begin={warmup_epochs},",
            "        end=100",
            "    )",
            "]"
        ]

        return "\n".join(config_lines)

    def _generate_generic_config(self, params: list, root_key: str) -> str:
        """生成通用配置"""
        config_lines = []

        for config_path, param_value, param_name in params:
            config_lines.append(f"# {param_name}: {self.hyperparameter_mapping[param_name]['description']}")
            config_lines.append(f"{config_path} = {param_value}")

        return "\n".join(config_lines)
    
    def run_training(self, config_path: str, trial: Optional[Trial] = None) -> Dict[str, Any]:
        """
        执行训练

        Args:
            config_path: 配置文件路径
            trial: Optuna trial对象（用于Early Pruning）

        Returns:
            训练结果字典
        """
        # 设置Early Pruning相关环境变量
        if trial is not None:
            os.environ['OPTUNA_TRIAL_NUMBER'] = str(trial.number)
            os.environ['OPTUNA_STUDY_NAME'] = self.study_name
            if self.storage_url:
                os.environ['OPTUNA_STORAGE_URL'] = self.storage_url
        # 构建训练命令
        if self.launcher == 'pytorch' and self.gpus:
            # 多卡PyTorch分布式训练
            gpu_count = len(self.gpus.split(',')) if ',' in self.gpus else 1
            cmd = [
                'torchrun',
                f'--nproc_per_node={gpu_count}',
                '--master_port=29500',
                "tools/train_m2.py",
                "--config", config_path,
                "--launcher", "pytorch",
                "--no-validate"
            ]
            # 设置GPU环境变量
            os.environ['CUDA_VISIBLE_DEVICES'] = self.gpus
        else:
            # 单卡训练（默认）
            cmd = [
                sys.executable, "tools/train_m2.py",
                "--config", config_path,
                "--launcher", self.launcher,
                "--no-validate"
            ]
            if self.gpus and self.launcher == 'none':
                os.environ['CUDA_VISIBLE_DEVICES'] = self.gpus
        
        logger.info(f"Running training command: {' '.join(cmd)}")
        
        try:
            # 执行训练
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=3600,  # 1小时超时
                cwd=os.getcwd()
            )
            
            if result.returncode != 0:
                logger.error(f"Training failed with return code {result.returncode}")
                logger.error(f"Error output: {result.stderr}")
                raise RuntimeError(f"Training failed: {result.stderr}")
            
            logger.info("Training completed successfully")
            return {"status": "success", "stdout": result.stdout, "stderr": result.stderr}
            
        except subprocess.TimeoutExpired:
            logger.error("Training timeout")
            raise RuntimeError("Training timeout")
        except Exception as e:
            logger.error(f"Training error: {str(e)}")
            raise
    
    def extract_metrics(self, work_dir: str) -> float:
        """
        从训练结果中提取评估指标

        Args:
            work_dir: 工作目录路径

        Returns:
            优化目标值（越小越好，这里返回负的准确率）
        """
        # 查找最新的日志文件
        log_files = list(Path(work_dir).glob("*.log"))
        if not log_files:
            logger.warning(f"No log files found in {work_dir}")
            return float('inf')  # 返回最差值

        latest_log = max(log_files, key=os.path.getctime)

        try:
            with open(latest_log, 'r', encoding='utf-8') as f:
                log_content = f.read()

            # 改进的指标提取逻辑
            best_acc = self._extract_best_accuracy(log_content)
            if best_acc is not None:
                # 返回负的准确率，因为optuna要最小化目标
                logger.info(f"Extracted best accuracy: {best_acc:.4f}")
                return -best_acc

            # 如果找不到准确率，尝试提取损失值
            best_loss = self._extract_best_loss(log_content)
            if best_loss is not None:
                logger.info(f"Extracted best loss: {best_loss:.4f}")
                return best_loss

            logger.warning("Could not extract any meaningful metrics")
            return float('inf')

        except Exception as e:
            logger.error(f"Error extracting metrics: {str(e)}")
            return float('inf')

    def _extract_best_accuracy(self, log_content: str) -> Optional[float]:
        """从日志中提取最佳准确率"""
        import re

        # 寻找准确率相关的模式
        acc_patterns = [
            r'acc[^:]*:\s*([0-9.]+)',
            r'accuracy[^:]*:\s*([0-9.]+)',
            r'val/acc[^:]*:\s*([0-9.]+)',
            r'val_acc[^:]*:\s*([0-9.]+)'
        ]

        best_acc = None
        for pattern in acc_patterns:
            matches = re.findall(pattern, log_content, re.IGNORECASE)
            if matches:
                accuracies = [float(match) for match in matches]
                current_best = max(accuracies)
                if best_acc is None or current_best > best_acc:
                    best_acc = current_best

        return best_acc

    def _extract_best_loss(self, log_content: str) -> Optional[float]:
        """从日志中提取最佳损失值"""
        import re

        # 寻找损失相关的模式
        loss_patterns = [
            r'loss[^:]*:\s*([0-9.]+)',
            r'val/loss[^:]*:\s*([0-9.]+)',
            r'val_loss[^:]*:\s*([0-9.]+)'
        ]

        best_loss = None
        for pattern in loss_patterns:
            matches = re.findall(pattern, log_content, re.IGNORECASE)
            if matches:
                losses = [float(match) for match in matches]
                current_best = min(losses)  # 损失越小越好
                if best_loss is None or current_best < best_loss:
                    best_loss = current_best

        return best_loss
    
    def objective(self, trial: Trial) -> float:
        """
        Optuna目标函数
        
        Args:
            trial: optuna trial对象
            
        Returns:
            优化目标值
        """
        logger.info(f"Starting trial {trial.number}")
        
        try:
            # 1. 建议超参数
            hyperparams = self.suggest_hyperparameters(trial)
            logger.info(f"Trial {trial.number} hyperparameters: {hyperparams}")
            
            # 2. 创建配置文件（传递trial对象以启用Early Pruning）
            config_path = self.create_config(hyperparams, trial.number, trial)
            
            # 3. 执行训练（传递trial对象以启用Early Pruning）
            training_result = self.run_training(config_path, trial)
            
            # 4. 提取指标
            work_dir = os.path.join(self.work_dir_base, f"trial_{trial.number}")
            objective_value = self.extract_metrics(work_dir)
            
            logger.info(f"Trial {trial.number} completed with objective value: {objective_value}")
            return objective_value
            
        except Exception as e:
            logger.error(f"Trial {trial.number} failed: {str(e)}")
            return float('inf')  # 返回最差值


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Optuna超参数优化训练')
    parser.add_argument('--config',
                       default='configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion.py',
                       help='基础配置文件路径')
    parser.add_argument('--study-name', default='mmaction2_hyperopt', help='Study名称')
    parser.add_argument('--n-trials', type=int, default=20, help='试验次数')
    parser.add_argument('--storage', help='Optuna存储URL')
    parser.add_argument('--optimization-group',
                       default=DEFAULT_OPTIMIZATION_GROUP,
                       choices=list(PARAMETER_GROUPS.keys()),
                       help='优化参数组')
    parser.add_argument('--list-groups', action='store_true', help='列出所有参数组')
    parser.add_argument('--launcher', 
                       choices=['none', 'pytorch', 'slurm', 'mpi'],
                       default='none',
                       help='分布式训练启动器')
    parser.add_argument('--gpus', 
                       help='GPU设备ID，如 "0,1,2,3" 或单卡 "0"')

    args = parser.parse_args()

    # 如果请求列出参数组
    if args.list_groups:
        print("Available parameter groups:")
        for group_name, params in PARAMETER_GROUPS.items():
            print(f"  {group_name}: {len(params)} parameters")
            for param in params:
                if param in HYPERPARAMETER_MAPPING:
                    desc = HYPERPARAMETER_MAPPING[param]['description']
                    print(f"    - {param}: {desc}")
        return
    
    # 创建训练管理器
    trainer = OptunaTrainer(
        base_config_path=args.config,
        study_name=args.study_name,
        storage_url=args.storage,
        optimization_group=args.optimization_group,
        launcher=getattr(args, 'launcher', 'none'),
        gpus=getattr(args, 'gpus', None)
    )

    # 安全创建或加载study（解决多卡环境下的数据库锁定问题）
    if args.storage and args.storage.startswith('sqlite:///'):
        # SQLite存储：使用安全管理器
        db_path = args.storage.replace('sqlite:///', '')
        logger.info(f"🔒 使用安全SQLite存储管理器: {db_path}")
        study = create_safe_optuna_study(
            study_name=args.study_name,
            storage_path=db_path,
            direction='minimize'
        )
    else:
        # 其他存储类型：使用标准方式，集成Early Pruning
        study = optuna.create_study(
            study_name=args.study_name,
            storage=args.storage,
            direction='minimize',  # 最小化损失
            load_if_exists=True,
            pruner=optuna.pruners.MedianPruner(
                n_startup_trials=5,  # 前5个trial不进行剪枝
                n_warmup_steps=10,   # 前10个epoch不进行剪枝
                interval_steps=1     # 每个epoch都检查剪枝
            )
        )
    
    logger.info(f"Starting optimization with {args.n_trials} trials")
    
    # 开始优化
    study.optimize(trainer.objective, n_trials=args.n_trials)
    
    # 输出最佳结果
    logger.info("Optimization completed!")
    logger.info(f"Best trial: {study.best_trial.number}")
    logger.info(f"Best value: {study.best_value}")
    logger.info(f"Best params: {study.best_params}")


if __name__ == '__main__':
    main()
