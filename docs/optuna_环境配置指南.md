# Optuna超参数优化环境配置指南

> 🔧 **环境配置** - 从零开始搭建完整的优化环境

## 📋 目录
- [系统要求](#系统要求)
- [Python环境配置](#python环境配置)
- [核心依赖安装](#核心依赖安装)
- [MMAction2环境](#mmaction2环境)
- [Optuna框架安装](#optuna框架安装)
- [环境验证](#环境验证)
- [常见问题](#常见问题)

## 🖥️ 系统要求

### 硬件要求
- **CPU**: Intel/AMD x64架构
- **内存**: 最低8GB，推荐16GB+
- **存储**: 可用空间20GB+
- **GPU**: NVIDIA GPU（支持CUDA）推荐，CPU训练也可以

### GPU推荐配置
| GPU配置 | 训练速度 | 推荐试验数 |
|---------|----------|------------|
| 1张RTX 4060 Ti | 基准速度 | 10-20次 |
| 2张RTX 4060 Ti | 约2倍速度 | 20-40次 |
| 4张RTX 4060 Ti | 约4倍速度 | 40-80次 |

### 操作系统支持
- ✅ **Linux** (Ubuntu 18.04+, CentOS 7+) - **推荐**
- ✅ **macOS** (10.14+)
- ✅ **Windows** (10/11)

## 🐍 Python环境配置

### 1. Python版本要求
```bash
# 检查Python版本
python --version
# 要求: Python 3.8-3.11 (推荐3.9)
```

### 2. Conda环境创建（推荐）
```bash
# 创建新环境
conda create -n optuna_env python=3.9 -y

# 激活环境
conda activate optuna_env

# 验证环境
which python
# 应该显示conda环境路径
```

### 3. 基础包管理工具
```bash
# 更新pip
pip install --upgrade pip

# 安装构建工具
pip install wheel setuptools
```

## 🔥 核心依赖安装

### 1. PyTorch环境（根据GPU情况选择）

#### CUDA版本查看
```bash
# 查看CUDA版本
nvidia-smi
# 或
nvcc --version
```

#### PyTorch安装
```bash
# CUDA 12.1版本（RTX 4060 Ti推荐）
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# CUDA 11.8版本
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# CPU版本（无GPU）
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
```

#### 验证PyTorch安装
```python
import torch
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")
print(f"GPU数量: {torch.cuda.device_count()}")
```

### 2. 基础科学计算包
```bash
pip install numpy scipy matplotlib pandas
pip install opencv-contrib-python Pillow
pip install einops decord
```

## 🎬 MMAction2环境

### 1. MMEngine安装
```bash
# 安装MMEngine
pip install mmengine>=0.7.1,<1.0.0
```

### 2. MMCV安装
```bash
# CUDA版本（推荐）
pip install mmcv>=2.0.0,<2.2.0 -f https://download.openmmlab.com/mmcv/dist/cu121/torch2.0/index.html

# CPU版本
pip install mmcv>=2.0.0,<2.2.0 -f https://download.openmmlab.com/mmcv/dist/cpu/torch2.0/index.html
```

### 3. MMAction2安装
```bash
# 切换到项目目录
cd /media/pyl/WD_Blue_1T/All_proj/TimeConv_classify/mmaction2_main0808

# 安装MMAction2
pip install -e .
```

### 4. 验证MMAction2安装
```python
import mmaction
print(f"MMAction2安装成功: {mmaction.__version__}")
```

## 🔍 Optuna框架安装

### 1. 核心Optuna包
```bash
# 安装Optuna核心包
pip install optuna==4.5.0

# 安装可视化Dashboard
pip install optuna-dashboard==0.19.0
```

### 2. 可选优化组件
```bash
# 高级采样器和剪枝器
pip install optuna[integration]

# 分布式优化支持
pip install optuna[distributed]
```

### 3. 分析和可视化工具
```bash
# 高级可视化
pip install plotly>=5.0.0
pip install seaborn>=0.11.0

# 数据处理
pip install pandas>=1.3.0
```

### 4. 验证Optuna安装
```python
import optuna
import optuna_dashboard
print(f"Optuna版本: {optuna.__version__}")
print(f"Dashboard版本: {optuna_dashboard.__version__}")
```

## 🌐 分布式训练环境

### 1. 多卡训练支持
```bash
# 检查GPU状态
nvidia-smi

# 安装torchrun（PyTorch 1.9+自带）
python -c "import torch.distributed.run; print('torchrun可用')"
```

### 2. 网络配置（可选）
```bash
# 设置主节点端口（如果默认端口被占用）
export MASTER_PORT=29501

# 设置网络接口（多网卡环境）
export NCCL_SOCKET_IFNAME=eth0
```

## ✅ 环境验证

### 1. 创建测试脚本
```bash
# 创建验证脚本
cat > test_environment.py << EOF
#!/usr/bin/env python3
import sys
import torch
import mmaction
import optuna
import optuna_dashboard
import mmcv
import mmengine

def test_environment():
    print("🔍 环境验证开始...")
    
    # Python版本
    print(f"Python版本: {sys.version}")
    
    # PyTorch环境
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
    
    # MMAction2环境
    print(f"MMAction2版本: {mmaction.__version__}")
    print(f"MMCV版本: {mmcv.__version__}")
    print(f"MMEngine版本: {mmengine.__version__}")
    
    # Optuna环境
    print(f"Optuna版本: {optuna.__version__}")
    print(f"Dashboard版本: {optuna_dashboard.__version__}")
    
    print("✅ 环境验证完成!")

if __name__ == "__main__":
    test_environment()
EOF

# 运行验证
python test_environment.py
```

### 2. 运行Optuna基础测试
```bash
# 运行基础功能测试
python tools/test_optuna_basic.py

# 预期输出: 5/5 测试通过
```

### 3. 测试训练脚本
```bash
# 测试原有训练脚本
python tools/train_m2.py \
    --config configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion.py \
    --work-dir work_dirs/test_run \
    --no-validate

# 如果成功启动训练，按Ctrl+C停止即可
```

## 🛠️ 常见问题

### Q1: CUDA版本不匹配
```bash
# 问题: RuntimeError: CUDA runtime version mismatch
# 解决: 重新安装匹配的PyTorch版本

# 查看CUDA版本
nvidia-smi  # 查看Driver版本
nvcc --version  # 查看Runtime版本

# 重新安装PyTorch
pip uninstall torch torchvision torchaudio
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
```

### Q2: MMCV编译错误
```bash
# 问题: MMCV安装失败
# 解决: 使用预编译版本

# 卸载现有版本
pip uninstall mmcv mmcv-full

# 安装预编译版本
pip install mmcv>=2.0.0 -f https://download.openmmlab.com/mmcv/dist/cu121/torch2.0/index.html
```

### Q3: Optuna Dashboard启动失败
```bash
# 问题: 端口被占用
# 解决: 更换端口

# 查看端口占用
netstat -tulpn | grep 8080

# 使用其他端口
python tools/optuna_dashboard_manager.py --port 8081
```

### Q4: 内存不足
```bash
# 问题: CUDA out of memory
# 解决: 调整批次大小

# 方法1: 减少批次大小
# 在配置文件中设置更小的batch_size

# 方法2: 使用梯度累积
# 在配置文件中设置accumulate_grad_batches

# 方法3: 使用混合精度
python tools/optuna_trainer.py --amp
```

### Q5: 网络连接问题
```bash
# 问题: 下载依赖包超时
# 解决: 使用国内镜像源

# 设置pip镜像源
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple

# 或使用conda镜像源
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/
```

## 📦 完整安装脚本

### 一键安装脚本（Linux/macOS）
```bash
#!/bin/bash
# optuna_env_setup.sh

echo "🚀 开始安装Optuna优化环境..."

# 创建conda环境
conda create -n optuna_env python=3.9 -y
conda activate optuna_env

# 基础包
pip install --upgrade pip wheel setuptools

# PyTorch (CUDA 12.1)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# 科学计算包
pip install numpy scipy matplotlib pandas
pip install opencv-contrib-python Pillow einops decord

# MMAction2依赖
pip install mmengine>=0.7.1,<1.0.0
pip install mmcv>=2.0.0,<2.2.0 -f https://download.openmmlab.com/mmcv/dist/cu121/torch2.0/index.html

# Optuna框架
pip install optuna==4.5.0
pip install optuna-dashboard==0.19.0
pip install plotly seaborn

# 安装MMAction2
cd /media/pyl/WD_Blue_1T/All_proj/TimeConv_classify/mmaction2_main0808
pip install -e .

echo "✅ 环境安装完成!"
echo "🔍 运行验证: python test_environment.py"
```

### 使用安装脚本
```bash
# 下载并运行
chmod +x optuna_env_setup.sh
./optuna_env_setup.sh
```

## 🎯 环境配置最佳实践

### 1. 版本管理
- 使用conda管理Python环境
- 固定主要依赖版本
- 定期备份环境配置

### 2. GPU环境优化
- 确保CUDA Driver ≥ CUDA Runtime版本
- 使用合适的PyTorch版本
- 监控GPU内存使用

### 3. 存储配置
- 确保项目目录有足够空间（20GB+）
- 使用SSD提升I/O性能
- 定期清理临时文件

### 4. 网络配置
- 配置稳定的网络连接
- 使用镜像源加速下载
- 处理防火墙和代理设置

---

**配置完成后，您就可以开始使用Optuna超参数优化了！** 🎉

下一步：查看 [使用指南](optuna_使用指南.md) 开始您的第一次优化。
