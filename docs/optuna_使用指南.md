# Optuna超参数优化使用指南

> 📌 **简洁实用** - 面向用户的快速上手指南

## 🚀 快速开始

### 1. 环境准备
```bash
# 激活环境
source /home/<USER>/anaconda3/etc/profile.d/conda.sh
conda activate torch
```

### 2. 基础使用（推荐新手）

#### 单卡快速测试
```bash
# 3次试验，快速验证（约1-3小时）
python tools/optuna_trainer.py --n-trials 3
```

#### 单卡标准优化
```bash
# 10次试验，寻找较好参数（约半天）
python tools/optuna_trainer.py \
    --n-trials 10 \
    --study-name my_optimization \
    --storage sqlite:///work_dirs/optuna_studies.db
```

### 3. 多卡加速（推荐有多GPU用户）

#### 4卡并行训练
```bash
# 大幅提升速度，20次试验（约半天，相当于单卡2天）
python tools/optuna_trainer.py \
    --launcher pytorch \
    --gpus "0,1,2,3" \
    --n-trials 20 \
    --study-name gpu4_optimization \
    --storage sqlite:///work_dirs/optuna_studies.db
```

#### 2卡训练
```bash
python tools/optuna_trainer.py \
    --launcher pytorch \
    --gpus "0,1" \
    --n-trials 15
```

## 📊 实时监控

### 启动可视化Dashboard
```bash
# 在另一个终端窗口运行
python tools/optuna_dashboard_manager.py \
    --action start \
    --storage sqlite:///work_dirs/optuna_studies.db

# 浏览器访问: http://localhost:8080
```

## 📈 结果分析

### 查看最佳结果
```bash
# 查看study概况
python tools/optuna_dashboard_manager.py \
    --action info \
    --study-name my_optimization \
    --storage sqlite:///work_dirs/optuna_studies.db
```

### 生成详细报告
```bash
# 生成图表和分析报告
python tools/optuna_analyzer.py \
    --study-name my_optimization \
    --storage sqlite:///work_dirs/optuna_studies.db \
    --action report
```

## 🎯 参数组选择

### 查看可用参数组
```bash
python tools/optuna_trainer.py --list-groups
```

### 按需选择优化参数

#### 学习率专项优化
```bash
python tools/optuna_trainer.py \
    --optimization-group learning_rate \
    --n-trials 15
```

#### 模型结构优化
```bash
python tools/optuna_trainer.py \
    --optimization-group model_structure \
    --n-trials 20
```

#### 高优先级参数（默认）
```bash
python tools/optuna_trainer.py \
    --optimization-group high_priority \
    --n-trials 25
```

## ⏱️ 时间规划参考

| 试验次数 | 单卡时间 | 4卡时间 | 适用场景 |
|---------|----------|---------|----------|
| 3次 | 1-3小时 | 20-45分钟 | 快速测试 |
| 10次 | 5-10小时 | 1-2.5小时 | 标准优化 |
| 20次 | 10-20小时 | 2.5-5小时 | 深度优化 |
| 50次 | 1-3天 | 6-18小时 | 全面搜索 |

## 🔧 常用命令组合

### 完整优化流程
```bash
# 步骤1: 启动Dashboard（终端1）
python tools/optuna_dashboard_manager.py --action start

# 步骤2: 运行优化（终端2）
python tools/optuna_trainer.py \
    --launcher pytorch \
    --gpus "0,1,2,3" \
    --n-trials 20 \
    --study-name production_run \
    --storage sqlite:///work_dirs/optuna_studies.db

# 步骤3: 生成报告
python tools/optuna_analyzer.py \
    --study-name production_run \
    --action report
```

### 继续之前的优化
```bash
# 使用相同的study-name继续优化
python tools/optuna_trainer.py \
    --study-name production_run \
    --n-trials 10 \
    --storage sqlite:///work_dirs/optuna_studies.db
```

## 📍 重要提醒

### 存储路径说明
- `sqlite:///work_dirs/optuna_studies.db` 
- 实际位置：`项目根目录/work_dirs/optuna_studies.db`
- **重要**：使用完整的storage参数确保结果持久化

### 试验理解
- **1次trial = 1个完整训练过程**（从epoch 0到结束）
- 不是网格搜索，是智能优化算法
- 通常5-10次trials就能找到很好的结果

### 多卡注意事项
- 确保所有GPU可用：`nvidia-smi`
- 多卡训练需要：`--launcher pytorch`
- GPU编号用逗号分隔：`--gpus "0,1,2,3"`

## 🆘 常见问题

### Q: 如何知道优化是否收敛？
A: 查看Dashboard中的优化历史图，如果最后几次trial结果不再提升，说明已收敛。

### Q: 中途停止了怎么办？
A: 使用相同的study-name和storage继续运行即可。

### Q: 内存不足怎么办？
A: 减少batch_size或使用更少GPU。

### Q: 某次trial失败了？
A: Optuna会自动跳过失败的trial，继续优化。

## 📞 获取帮助

如有问题请查看：
- 技术文档：`docs/optuna_技术文档.md`
- 参数配置：`docs/optuna_参数配置指南.md`
- 项目维护者：Moss

---
**最后更新**: 2025-01-27  
**版本**: v3.0 - 多卡训练支持版
