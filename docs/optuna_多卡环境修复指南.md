# Optuna多卡环境修复指南

> 🔧 解决多卡服务器上SQLite数据库锁定问题的完整方案

## 问题描述

在多卡服务器上运行Optuna训练时，出现以下错误：
```
sqlalchemy.exc.OperationalError: database is locked
```

**根本原因**：多个GPU进程同时访问SQLite数据库文件，导致文件锁冲突。

## 解决方案总览

| 方案 | 适用场景 | 优点 | 缺点 | 推荐度 |
|------|----------|------|------|--------|
| Redis存储 | 生产环境 | 高并发、稳定 | 需要Redis服务 | ⭐⭐⭐⭐⭐ |
| 安全SQLite | 开发环境 | 简单、无依赖 | 性能略低 | ⭐⭐⭐⭐ |
| 内存存储 | 测试环境 | 快速、无锁 | 不持久化 | ⭐⭐⭐ |

## 方案1：Redis存储（推荐）

### 1.1 安装Redis
```bash
# Docker方式（推荐）
docker run -d -p 6379:6379 --name optuna-redis redis

# 或系统安装
sudo apt-get install redis-server
```

### 1.2 使用Redis存储
```bash
# 多卡训练
python tools/optuna_trainer.py \
    --storage redis://localhost:6379/0 \
    --launcher pytorch \
    --gpus "0,1,2,3" \
    --n-trials 20 \
    --study-name multicard_optimization
```

### 1.3 Redis配置选项
```bash
# 自定义Redis配置
export REDIS_HOST=localhost
export REDIS_PORT=6379
export REDIS_DB=0

# 使用密码认证
--storage redis://:password@localhost:6379/0
```

## 方案2：安全SQLite存储（已修复）

### 2.1 自动修复
现有的`optuna_trainer.py`已集成安全存储管理器，自动处理多进程锁定问题。

```bash
# 直接使用，无需额外配置
python tools/optuna_trainer.py \
    --storage sqlite:///work_dirs/optuna_studies.db \
    --launcher pytorch \
    --gpus "0,1,2,3" \
    --n-trials 20
```

### 2.2 工作原理
- **进程锁机制**：使用文件锁确保数据库访问互斥
- **主进程管理**：仅主进程创建study，其他进程等待
- **超时重试**：自动处理锁等待和重试逻辑

## 方案3：内存存储

### 3.1 适用场景
- 快速测试和调试
- 不需要持久化结果
- 避免所有存储相关问题

```bash
# 内存存储
python tools/optuna_trainer.py \
    --storage memory \
    --launcher pytorch \
    --gpus "0,1,2,3" \
    --n-trials 10
```

### 3.2 注意事项
- ⚠️ 结果不会保存到磁盘
- ⚠️ 进程结束后数据丢失
- ✅ 无并发冲突问题

## 快速修复工具

### 使用修复脚本
```bash
# 运行修复脚本
bash fix_multicard_optuna.sh

# 检查环境
python tools/optuna_multicard_fix.py --action check

# 获取推荐方案
python tools/optuna_multicard_fix.py --action recommend

# 测试存储方案
python tools/optuna_multicard_fix.py --action test --storage-type auto
```

## 环境检查清单

### 多卡环境确认
```bash
# 检查GPU
nvidia-smi

# 检查环境变量
echo "RANK: $RANK"
echo "WORLD_SIZE: $WORLD_SIZE" 
echo "LOCAL_RANK: $LOCAL_RANK"
```

### 依赖检查
```bash
# Python包
pip install optuna redis

# Redis服务
redis-cli ping
```

## 常见问题解决

### Q1: 仍然出现数据库锁定错误
**解决方案**：
1. 确认使用了修复后的`optuna_trainer.py`
2. 切换到Redis存储方案
3. 检查是否有其他进程占用数据库文件

### Q2: Redis连接失败
**解决方案**：
```bash
# 检查Redis状态
systemctl status redis
# 或
docker ps | grep redis

# 测试连接
redis-cli ping
```

### Q3: 多进程启动失败
**解决方案**：
```bash
# 检查端口占用
netstat -tulpn | grep 29500

# 更换端口
export MASTER_PORT=29501
```

### Q4: 内存不足
**解决方案**：
- 减少batch_size
- 使用更少GPU：`--gpus "0,1"`
- 启用梯度累积

## 性能对比

| 存储方案 | 并发性能 | 启动速度 | 稳定性 | 资源占用 |
|----------|----------|----------|--------|----------|
| Redis | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 中等 |
| 安全SQLite | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 低 |
| 内存存储 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | 低 |

## 最佳实践

### 生产环境推荐配置
```bash
# Redis + 4卡训练
python tools/optuna_trainer.py \
    --config configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion.py \
    --storage redis://localhost:6379/0 \
    --launcher pytorch \
    --gpus "0,1,2,3" \
    --n-trials 50 \
    --study-name production_hyperopt \
    --optimization-group learning_rate
```

### 开发环境推荐配置
```bash
# 安全SQLite + 2卡训练
python tools/optuna_trainer.py \
    --storage sqlite:///work_dirs/optuna_studies.db \
    --launcher pytorch \
    --gpus "0,1" \
    --n-trials 20 \
    --study-name dev_hyperopt
```

### 测试环境推荐配置
```bash
# 内存存储 + 单卡
python tools/optuna_trainer.py \
    --storage memory \
    --gpus "0" \
    --n-trials 5 \
    --study-name test_hyperopt
```

## 监控和调试

### 查看训练进度
```bash
# 启动Dashboard
python tools/optuna_dashboard_manager.py \
    --storage redis://localhost:6379/0 \
    --host 0.0.0.0 \
    --port 8080
```

### 日志分析
```bash
# 查看详细日志
export PYTHONPATH=$PWD:$PYTHONPATH
python tools/optuna_trainer.py --help
```

---

**更新时间**: 2025-01-27  
**版本**: v1.0 - 多卡环境修复版  
**维护者**: Moss
