# Optuna参数配置指南

> 📝 **参数定制** - 如何添加和修改超参数搜索空间

## 📍 快速参考

### 查看可用参数组
```bash
python tools/optuna_trainer.py --list-groups
```

### 使用特定参数组
```bash
# 学习率专项优化
python tools/optuna_trainer.py --optimization-group learning_rate --n-trials 15

# 高优先级参数（默认）
python tools/optuna_trainer.py --optimization-group high_priority --n-trials 20

# 模型结构优化
python tools/optuna_trainer.py --optimization-group model_structure --n-trials 25
```

## 🎯 当前支持的参数组

| 参数组名称 | 参数数量 | 优化重点 | 推荐trials |
|-----------|----------|----------|------------|
| `high_priority` | 7个 | 影响最大的核心参数 | 15-30 |
| `learning_rate` | 5个 | 各模块学习率 | 10-20 |
| `regularization` | 3个 | 正则化和dropout | 8-15 |
| `model_structure` | 4个 | 模型结构维度 | 15-25 |
| `training_strategy` | 4个 | 训练策略 | 10-20 |

## 🔧 配置文件位置

**主配置文件**: `configs/recognition/Multimodal/optuna_templates/hyperparameter_mapping.py`

## 📝 参数映射格式

每个超参数定义格式：
```python
'parameter_name': {
    'config_path': '配置文件路径',           # 如 'model.fusion_neck.dropout'
    'type': 'suggest_float',               # suggest方法类型
    'params': {'low': 0.1, 'high': 0.8},  # 搜索范围
    'description': '参数描述'
}
```

## 🎯 核心参数示例

### 学习率参数
```python
# 基础学习率
'base_lr': {
    'config_path': 'optim_wrapper.optimizer.lr',
    'type': 'suggest_float',
    'params': {'low': 1e-5, 'high': 1e-2, 'log': True}
}

# 图像骨干网络学习率倍数
'image_backbone_lr_mult': {
    'config_path': 'optim_wrapper.paramwise_cfg.custom_keys.image_backbone.lr_mult',
    'type': 'suggest_float',
    'params': {'low': 0.05, 'high': 0.2}
}
```

### 正则化参数
```python
# 权重衰减
'weight_decay': {
    'config_path': 'optim_wrapper.optimizer.weight_decay',
    'type': 'suggest_float',
    'params': {'low': 1e-5, 'high': 1e-3, 'log': True}
}

# Dropout率
'fusion_neck_dropout': {
    'config_path': 'model.fusion_neck.dropout',
    'type': 'suggest_float',
    'params': {'low': 0.3, 'high': 0.7}
}
```

### 模型结构参数
```python
# 融合特征维度
'fusion_dim': {
    'config_path': 'model.fusion_neck.fusion_dim',
    'type': 'suggest_categorical',
    'params': {'choices': [256, 384, 512, 768, 1024]}
}

# 批次大小
'batch_size': {
    'config_path': 'train_dataloader.batch_size',
    'type': 'suggest_categorical',
    'params': {'choices': [2, 4, 6, 8]}
}
```

## ➕ 添加新参数

### 1. 编辑配置文件
打开 `configs/recognition/Multimodal/optuna_templates/hyperparameter_mapping.py`

### 2. 添加新参数定义
```python
# 在HYPERPARAMETER_MAPPING中添加
'my_new_param': {
    'config_path': 'model.some_module.new_param',
    'type': 'suggest_float',
    'params': {'low': 0.1, 'high': 1.0},
    'description': '我的新参数'
}
```

### 3. 添加到参数组
```python
# 在PARAMETER_GROUPS中添加到相应组
PARAMETER_GROUPS['custom_group'] = ['my_new_param', 'base_lr']
```

### 4. 测试新参数
```bash
python tools/optuna_trainer.py \
    --optimization-group custom_group \
    --n-trials 3
```

## 🔍 支持的suggest类型

### suggest_float
```python
'params': {'low': 0.1, 'high': 1.0}              # 线性分布
'params': {'low': 1e-5, 'high': 1e-2, 'log': True}  # 对数分布
```

### suggest_int
```python
'params': {'low': 1, 'high': 100}                # 整数范围
'params': {'low': 8, 'high': 128, 'step': 8}     # 指定步长
```

### suggest_categorical
```python
'params': {'choices': [16, 32, 64, 128]}          # 数值选择
'params': {'choices': ['adam', 'sgd', 'adamw']}   # 字符串选择
```

## 📊 配置路径示例

### 优化器配置
- `optim_wrapper.optimizer.lr` - 学习率
- `optim_wrapper.optimizer.weight_decay` - 权重衰减
- `optim_wrapper.paramwise_cfg.custom_keys.{module}.lr_mult` - 模块学习率倍数

### 模型配置
- `model.fusion_neck.dropout` - 融合模块dropout
- `model.fusion_neck.fusion_dim` - 融合维度
- `model.cls_head.num_classes` - 分类数量

### 数据加载器配置
- `train_dataloader.batch_size` - 训练批次大小
- `val_dataloader.batch_size` - 验证批次大小

### 学习率调度器配置
- `param_scheduler.0.end` - 预热轮数
- `param_scheduler.1.T_max` - 余弦退火周期

## 🎯 最佳实践

### 参数搜索范围设置
1. **学习率**: 使用对数分布，范围通常 [1e-5, 1e-2]
2. **Dropout**: 线性分布，范围通常 [0.1, 0.8]
3. **批次大小**: 使用2的幂次，如 [2, 4, 8, 16]
4. **维度参数**: 使用常见的维度值，如 [256, 512, 1024]

### 参数分组原则
1. **高优先级**: 对性能影响最大的参数
2. **专项优化**: 按功能分组（学习率、正则化等）
3. **依赖关系**: 相互影响的参数放在同一组

### 优化策略
1. **先高后低**: 先优化高优先级参数
2. **分步优化**: 不同阶段优化不同参数组
3. **验证结果**: 每组优化后验证效果

## 📞 获取帮助

- **查看完整配置**: 查看 `hyperparameter_mapping.py` 文件
- **技术文档**: `docs/optuna_技术文档.md`
- **使用指南**: `docs/optuna_使用指南.md`

---
**最后更新**: 2025-01-27  
**版本**: v3.0 - 简化配置版