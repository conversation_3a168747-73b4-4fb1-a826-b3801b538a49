# Optuna超参数优化技术文档

> 🔧 **技术实现** - 面向开发者的详细技术文档

## 📋 目录
- [项目概述](#项目概述)
- [技术架构](#技术架构)
- [核心组件](#核心组件)
- [多卡训练实现](#多卡训练实现)
- [优化算法详解](#优化算法详解)
- [文件结构](#文件结构)
- [多卡环境问题解决](#多卡环境问题解决)
- [开发指南](#开发指南)

## 项目概述

### 设计目标
- **非侵入性设计**：完全不修改现有训练代码
- **智能优化**：使用TPE算法替代网格搜索
- **分布式支持**：支持多卡并行训练
- **可视化监控**：实时监控优化过程
- **模块化架构**：各组件独立，易于扩展

### 技术栈
- **优化引擎**：Optuna 4.5.0
- **可视化**：optuna-dashboard 0.19.0
- **训练框架**：MMAction2 + MMEngine
- **分布式**：PyTorch DDP
- **数据存储**：SQLite / Redis / MySQL / PostgreSQL
- **多卡支持**：安全存储管理器 + 进程锁机制

## 技术架构

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    Optuna优化框架                             │
├─────────────────────────────────────────────────────────────┤
│  optuna_trainer.py (核心管理器)                               │
│  ├── 超参数建议 (TPE算法)                                      │
│  ├── 配置文件生成                                             │
│  ├── 训练进程管理                                             │
│  ├── 结果提取                                                │
│  └── 自动存储修复                                             │
├─────────────────────────────────────────────────────────────┤
│  optuna_storage_manager.py (存储管理器) 🆕                    │
│  ├── 安全SQLite存储                                           │
│  ├── 多进程管理                                              │
│  ├── 文件锁机制                                              │
│  └── Redis存储支持                                           │
├─────────────────────────────────────────────────────────────┤
│  hyperparameter_mapping.py (参数映射)                        │
│  ├── 参数定义                                                │
│  ├── 搜索空间                                                │
│  └── 参数分组                                                │
├─────────────────────────────────────────────────────────────┤
│  原有训练系统 (train_m2.py)                                   │
│  ├── 单卡训练                                                │
│  ├── 多卡训练 (PyTorch DDP)                                   │
│  └── MMAction2框架                                           │
└─────────────────────────────────────────────────────────────┘
```

### 数据流程
```
Trial开始 → 生成超参数 → 创建配置文件 → 启动训练进程 → 提取结果 → 返回目标值
    ↑                                                              ↓
    └─────────────── TPE算法分析历史结果，生成下一组参数 ←─────────────┘
```

## 核心组件

### 1. OptunaTrainer (核心管理器)

```python
class OptunaTrainer:
    """Optuna训练管理器 - 系统核心"""
    
    def __init__(self, base_config_path, launcher='none', gpus=None):
        """
        Args:
            base_config_path: 基础配置文件路径
            launcher: 分布式启动器 ('none', 'pytorch', 'slurm', 'mpi')
            gpus: GPU设备，如 '0,1,2,3'
        """
    
    def suggest_hyperparameters(self, trial) -> Dict[str, Any]:
        """基于映射配置动态建议超参数"""
    
    def create_config(self, hyperparams, trial_id) -> str:
        """基于超参数创建完整配置文件"""
    
    def run_training(self, config_path) -> Dict[str, Any]:
        """执行训练（支持单卡/多卡）"""
    
    def extract_metrics(self, work_dir) -> float:
        """从训练日志中提取优化目标"""
    
    def objective(self, trial) -> float:
        """Optuna目标函数 - 完整优化流程"""
```

### 2. 超参数映射系统

#### 映射配置格式
```python
HYPERPARAMETER_MAPPING = {
    'parameter_name': {
        'config_path': '配置文件路径',     # 如 'model.fusion_neck.dropout'
        'type': 'suggest_float',          # suggest方法类型
        'params': {'low': 0.1, 'high': 0.8},  # 搜索范围
        'description': '参数描述'
    }
}
```

#### 参数组系统
```python
PARAMETER_GROUPS = {
    'high_priority': [7个高影响参数],
    'learning_rate': [5个学习率参数],
    'model_structure': [4个结构参数],
    'regularization': [3个正则化参数]
}
```

### 3. 配置生成器

#### 智能配置更新
```python
def _generate_config_updates(self, hyperparams):
    """
    基于超参数映射生成配置更新代码
    支持复杂的配置路径，如：
    - optim_wrapper.paramwise_cfg.custom_keys.{module}.lr_mult
    - model.fusion_neck.dropout
    - train_dataloader.batch_size
    """
```

## 多卡训练实现

### 架构设计
```python
def run_training(self, config_path):
    if self.launcher == 'pytorch' and self.gpus:
        # 多卡PyTorch分布式训练
        gpu_count = len(self.gpus.split(','))
        cmd = [
            'torchrun',
            f'--nproc_per_node={gpu_count}',
            '--master_port=29500',
            "tools/train_m2.py",
            "--config", config_path,
            "--launcher", "pytorch"
        ]
        os.environ['CUDA_VISIBLE_DEVICES'] = self.gpus
    else:
        # 单卡训练
        cmd = [sys.executable, "tools/train_m2.py", "--config", config_path]
```

### 支持的启动方式
- **none**: 单卡训练（默认）
- **pytorch**: PyTorch DDP多卡训练
- **slurm**: SLURM集群调度
- **mpi**: MPI分布式训练

### GPU配置
- **单卡**: `--gpus "0"`
- **多卡**: `--gpus "0,1,2,3"`
- **自动**: `--gpus "auto"`

## 优化算法详解

### TPE算法 vs 网格搜索

#### 网格搜索（传统方法）
```python
# 固定网格点
learning_rates = [1e-3, 5e-4, 1e-5]
dropouts = [0.4, 0.5, 0.6]
# 总组合: 3×3 = 9次试验
```

#### TPE算法（Optuna）
```python
# 连续空间采样
learning_rate = trial.suggest_float('lr', 1e-5, 1e-3, log=True)
dropout = trial.suggest_float('dropout', 0.3, 0.7)
# 智能采样，可能在5次试验内找到最优解
```

### TPE工作原理
1. **随机探索阶段**（前2-3次试验）：在整个搜索空间随机采样
2. **智能优化阶段**（后续试验）：
   - 分析历史试验结果
   - 建立优秀/普通参数的概率模型
   - 采样最有希望的参数区域
3. **收敛阶段**：在最优区域精细搜索

### Early Pruning机制
```python
# 在OptunaHook中实现
class OptunaHook:
    def after_train_epoch(self, runner):
        # 如果当前epoch的性能远低于历史最佳，提前终止
        current_metric = runner.message_hub.get_info('train/acc')
        trial.report(current_metric, step=runner.epoch)
        if trial.should_prune():
            raise optuna.TrialPruned()
```

## 文件结构

### 核心文件
```
tools/
├── optuna_trainer.py              # 核心管理器
├── optuna_dashboard_manager.py    # Dashboard管理
├── optuna_analyzer.py             # 结果分析
└── config_path_resolver.py        # 配置路径解析

configs/recognition/Multimodal/optuna_templates/
├── hyperparameter_mapping.py      # 参数映射配置
└── base_template.py               # 基础配置模板

mmaction/engine/hooks/
└── optuna_hook.py                 # MMEngine集成Hook

work_dirs/
├── optuna_studies.db              # SQLite数据库
├── optuna_trials/                 # 试验工作目录
│   ├── trial_0/
│   ├── trial_1/
│   └── ...
└── analysis/                      # 分析结果
    ├── optimization_history.png
    ├── parameter_importance.png
    └── analysis_report.md
```

### 生成的临时文件
```
work_dirs/optuna_trials/trial_N/
├── trial_N_config.py             # 生成的配置文件
├── *.log                         # 训练日志
├── *.pth                         # 模型权重
└── events.out.tfevents.*         # TensorBoard日志
```

## 开发指南

### 添加新的超参数

#### 1. 在hyperparameter_mapping.py中添加
```python
'new_parameter': {
    'config_path': 'model.some_module.new_param',
    'type': 'suggest_float',
    'params': {'low': 0.1, 'high': 1.0},
    'description': '新参数描述'
}
```

#### 2. 添加到参数组
```python
PARAMETER_GROUPS['custom_group'] = ['new_parameter', 'base_lr']
```

#### 3. 测试新参数
```bash
python tools/optuna_trainer.py \
    --optimization-group custom_group \
    --n-trials 3
```

### 扩展配置生成器

#### 支持新的配置结构
```python
def _generate_custom_config(self, params: list) -> str:
    """为新的配置结构生成更新代码"""
    # 实现自定义配置生成逻辑
    pass
```

### 集成新的训练脚本

#### 修改run_training方法
```python
def run_training(self, config_path):
    cmd = [
        sys.executable, "tools/your_custom_train.py",
        "--config", config_path
    ]
    # 其他训练逻辑
```

### 自定义目标函数

#### 支持多目标优化
```python
def extract_metrics(self, work_dir):
    """提取多个优化目标"""
    accuracy = self._extract_accuracy(work_dir)
    inference_time = self._extract_inference_time(work_dir)
    
    # 多目标组合
    return -accuracy + 0.1 * inference_time
```

## 性能优化

### 训练速度优化
1. **多卡并行**：使用`--launcher pytorch --gpus "0,1,2,3"`
2. **Early Pruning**：提前终止性能差的试验
3. **批量优化**：适当增加batch_size
4. **禁用验证**：使用`--no-validate`

### 内存优化
1. **梯度累积**：配置gradient_accumulation_steps
2. **混合精度**：使用`--amp`
3. **数据加载**：优化num_workers

### 存储优化
1. **数据库选择**：生产环境使用MySQL/PostgreSQL
2. **定期清理**：清理旧的trial目录
3. **压缩存储**：压缩模型权重文件

## 监控和调试

### 日志系统
```python
import logging
logger = logging.getLogger(__name__)

# 关键节点日志
logger.info(f"Starting trial {trial.number}")
logger.info(f"Generated hyperparameters: {hyperparams}")
logger.info(f"Training completed with objective: {objective_value}")
```

### 性能监控
- **训练进度**：通过Dashboard实时监控
- **资源使用**：监控GPU/CPU/内存使用率
- **收敛分析**：观察优化历史曲线

### 错误处理
```python
try:
    training_result = self.run_training(config_path)
except subprocess.TimeoutExpired:
    logger.error("Training timeout")
    return float('inf')
except Exception as e:
    logger.error(f"Training error: {str(e)}")
    return float('inf')
```

## 多卡环境问题解决

### SQLite数据库锁定问题

#### 问题描述
多卡训练环境下，多个进程同时访问SQLite数据库导致锁定错误：
```
sqlalchemy.exc.OperationalError: database is locked
```

#### 技术原因
1. **多进程并发**：`torchrun`启动多个进程同时访问数据库
2. **SQLite限制**：不支持真正的并发写入
3. **文件锁冲突**：多进程创建/访问数据库文件时产生冲突

#### 解决方案架构

##### 1. 安全SQLite存储管理器
```python
class SafeSQLiteStorage:
    """安全的SQLite存储管理器"""

    def __init__(self, db_path: str, timeout: int = 30):
        self.db_path = os.path.abspath(db_path)
        self.lock_path = f"{self.db_path}.lock"
        self.timeout = timeout

    @contextmanager
    def acquire_lock(self):
        """获取文件锁"""
        lock_file = open(self.lock_path, 'w')
        try:
            fcntl.flock(lock_file.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
            yield
        finally:
            fcntl.flock(lock_file.fileno(), fcntl.LOCK_UN)
            lock_file.close()
```

##### 2. 多进程管理器
```python
class MultiProcessOptunaManager:
    """多进程Optuna管理器"""

    def __init__(self, storage_path: str):
        self.rank = int(os.environ.get('RANK', 0))
        self.world_size = int(os.environ.get('WORLD_SIZE', 1))

    def create_study(self, study_name: str) -> Optional[optuna.Study]:
        if self.rank == 0:
            # 仅主进程创建study
            return self.safe_storage.create_study_safe(study_name)
        else:
            # 非主进程等待主进程完成
            self._wait_for_study_creation(study_name)
            return None
```

#### 存储方案对比

| 方案 | 并发性能 | 稳定性 | 部署复杂度 | 适用场景 |
|------|----------|--------|------------|----------|
| Redis存储 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 中等 | 生产环境 |
| 安全SQLite | ⭐⭐⭐ | ⭐⭐⭐⭐ | 低 | 开发环境 |
| 内存存储 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | 低 | 测试环境 |

#### 自动修复集成
```python
# 在optuna_trainer.py中的自动修复逻辑
if args.storage and args.storage.startswith('sqlite:///'):
    # SQLite存储：使用安全管理器
    db_path = args.storage.replace('sqlite:///', '')
    logger.info(f"🔒 使用安全SQLite存储管理器: {db_path}")
    study = create_safe_optuna_study(
        study_name=args.study_name,
        storage_path=db_path,
        direction='minimize'
    )
else:
    # 其他存储类型：使用标准方式
    study = optuna.create_study(...)
```

### 修复工具集

#### 1. 诊断工具
```bash
# 环境检查
python tools/optuna_multicard_fix.py --action check

# 获取推荐方案
python tools/optuna_multicard_fix.py --action recommend

# 测试存储方案
python tools/optuna_multicard_fix.py --action test --storage-type auto
```

#### 2. 快速修复脚本
```bash
# 运行自动修复
bash fix_multicard_optuna.sh
```

#### 3. 新增文件结构
```
tools/
├── optuna_storage_manager.py      # 安全存储管理器
├── optuna_multicard_fix.py        # 多卡修复工具
└── optuna_trainer.py              # 已集成修复逻辑

docs/
└── optuna_多卡环境修复指南.md      # 详细修复指南

fix_multicard_optuna.sh             # 快速修复脚本
```

## 部署和运维

### 生产环境部署
1. **数据库配置**：使用MySQL替代SQLite
2. **分布式存储**：NFS共享存储
3. **任务调度**：SLURM集群管理
4. **监控告警**：集成Prometheus/Grafana

### 备份和恢复
```bash
# 备份study数据
python tools/optuna_dashboard_manager.py \
    --action export \
    --study-name production_study \
    --output backup_$(date +%Y%m%d).csv

# 恢复study（需要自定义脚本）
python tools/restore_study.py --input backup_20250127.csv
```

## 扩展开发

### 插件系统
支持自定义插件扩展功能：
- 自定义采样器
- 自定义剪枝器
- 自定义可视化组件

### API接口
提供REST API支持远程调用：
```python
from fastapi import FastAPI
app = FastAPI()

@app.post("/start_optimization")
async def start_optimization(config: OptimizationConfig):
    # 启动优化任务
    pass
```

---

**技术负责人**: Moss
**最后更新**: 2025-01-27
**版本**: v3.1 - 多卡环境修复版
**新增功能**: SQLite数据库锁定问题修复、Redis存储支持、多进程安全管理
