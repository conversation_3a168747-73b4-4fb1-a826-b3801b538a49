# Optuna超参数优化文档导航

> 🧭 **文档导航** - 快速找到您需要的文档

## 📚 文档结构概览

| 文档名称 | 适用人群 | 主要内容 |
|---------|----------|----------|
| 🚀 [使用指南](optuna_使用指南.md) | **用户** | 快速上手、常用命令、实用技巧 |
| 📝 [参数配置指南](optuna_参数配置指南.md) | **进阶用户** | 参数定制、搜索空间配置 |
| 🔧 [技术文档](optuna_技术文档.md) | **开发者** | 架构设计、技术实现、扩展开发 |
| 📋 [完整框架文档](optuna_超参调优框架.md) | **管理者** | 项目概述、实施记录、总结报告 |

## 🎯 按需选择文档

### 我是新手用户 👶
**推荐阅读**: [使用指南](optuna_使用指南.md)
- ✅ 3分钟快速上手
- ✅ 常用命令示例
- ✅ 时间规划参考
- ✅ 常见问题解答

### 我想自定义参数 🎛️
**推荐阅读**: [参数配置指南](optuna_参数配置指南.md)
- ✅ 查看所有可用参数
- ✅ 修改搜索范围
- ✅ 添加新的超参数
- ✅ 参数组选择策略

### 我是开发者 👨‍💻
**推荐阅读**: [技术文档](optuna_技术文档.md)
- ✅ 系统架构详解
- ✅ 多卡训练实现
- ✅ TPE算法原理
- ✅ 扩展开发指南

### 我需要项目总览 📊
**推荐阅读**: [完整框架文档](optuna_超参调优框架.md)
- ✅ 项目实施历程
- ✅ 测试验证结果
- ✅ 技术选型说明
- ✅ 安全保障措施

## 🚀 快速开始

### 1. 环境准备
```bash
source /home/<USER>/anaconda3/etc/profile.d/conda.sh
conda activate torch
```

### 2. 快速测试（推荐新手）
```bash
# 3次试验，验证环境（约1小时）
python tools/optuna_trainer.py --n-trials 3
```

### 3. 标准优化（单卡）
```bash
# 10次试验，寻找较好参数（约半天）
python tools/optuna_trainer.py \
    --n-trials 10 \
    --study-name my_study \
    --storage sqlite:///work_dirs/optuna_studies.db
```

### 4. 多卡加速（推荐有多GPU）
```bash
# 4卡并行，20次试验（相当于单卡80小时→20小时）
python tools/optuna_trainer.py \
    --launcher pytorch \
    --gpus "0,1,2,3" \
    --n-trials 20 \
    --study-name gpu4_study \
    --storage sqlite:///work_dirs/optuna_studies.db
```

## 📊 实时监控

```bash
# 启动可视化Dashboard
python tools/optuna_dashboard_manager.py \
    --action start \
    --storage sqlite:///work_dirs/optuna_studies.db

# 浏览器访问: http://localhost:8080
```

## 🔗 相关链接

### 核心工具文件
- `tools/optuna_trainer.py` - 核心训练管理器
- `tools/optuna_dashboard_manager.py` - 可视化管理
- `tools/optuna_analyzer.py` - 结果分析工具

### 配置文件
- `configs/recognition/Multimodal/optuna_templates/hyperparameter_mapping.py` - 参数映射配置
- `work_dirs/optuna_studies.db` - 结果存储数据库

### 项目信息
- **项目路径**: `/media/pyl/WD_Blue_1T/All_proj/TimeConv_classify/mmaction2_main0808`
- **创建时间**: 2025-08-23
- **最后更新**: 2025-01-27
- **维护者**: Moss

## 🆘 获取帮助

### 问题分类解决

#### 使用问题
- **文档**: [使用指南](optuna_使用指南.md) - 常见问题部分
- **命令**: `python tools/optuna_trainer.py --help`

#### 参数配置问题
- **文档**: [参数配置指南](optuna_参数配置指南.md)
- **命令**: `python tools/optuna_trainer.py --list-groups`

#### 技术实现问题
- **文档**: [技术文档](optuna_技术文档.md)
- **源码**: 查看 `tools/optuna_trainer.py` 注释

#### 项目整体问题
- **文档**: [完整框架文档](optuna_超参调优框架.md)
- **联系**: 项目维护者 Moss

## 📈 版本历史

- **v3.0** (2025-01-27): 多卡训练支持 + 文档重新整理
- **v2.1** (2025-08-25): 参数配置系统优化
- **v2.0** (2025-08-23): 完整框架实现
- **v1.0** (2025-08-23): 基础功能实现

## 🎯 核心特性

- ✅ **非侵入性设计** - 不修改原有训练代码
- ✅ **智能优化算法** - TPE算法替代网格搜索
- ✅ **多卡训练支持** - 支持1-8卡并行训练
- ✅ **实时可视化** - Dashboard监控优化过程
- ✅ **完整工具链** - 从优化到分析的完整流程
- ✅ **模块化设计** - 各组件独立，易于维护

---

**选择适合您的文档，开始optuna超参数优化之旅！** 🚀