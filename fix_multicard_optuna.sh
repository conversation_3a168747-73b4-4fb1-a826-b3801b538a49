#!/bin/bash
# Optuna多卡环境快速修复脚本
# 解决SQLite数据库锁定问题

set -e

echo "🔧 Optuna多卡环境修复工具"
echo "=========================="

# 检查当前环境
echo "📊 检查当前环境..."
echo "当前目录: $(pwd)"
echo "Python版本: $(python --version)"

# 检查是否在多卡环境中
if [ -n "$RANK" ] && [ -n "$WORLD_SIZE" ]; then
    echo "🎯 检测到多卡环境: Rank $RANK/$WORLD_SIZE"
    MULTICARD=true
else
    echo "📱 单卡环境"
    MULTICARD=false
fi

# 创建必要目录
echo "📁 创建工作目录..."
mkdir -p work_dirs
mkdir -p tools

# 检查Redis是否可用
echo "🔍 检查Redis可用性..."
if command -v redis-cli &> /dev/null && redis-cli ping &> /dev/null; then
    echo "✅ Redis可用"
    REDIS_AVAILABLE=true
else
    echo "❌ Redis不可用"
    REDIS_AVAILABLE=false
fi

# 提供解决方案
echo ""
echo "🎯 推荐解决方案:"
echo "=================="

if [ "$MULTICARD" = true ]; then
    echo "多卡环境检测到，推荐以下方案："
    echo ""
    
    if [ "$REDIS_AVAILABLE" = true ]; then
        echo "方案1: Redis存储（推荐）"
        echo "命令: python tools/optuna_trainer.py --storage redis://localhost:6379/0 --launcher pytorch --gpus \"0,1,2,3\" --n-trials 20"
        echo ""
    fi
    
    echo "方案2: 安全SQLite存储（已修复）"
    echo "命令: python tools/optuna_trainer.py --storage sqlite:///work_dirs/optuna_studies.db --launcher pytorch --gpus \"0,1,2,3\" --n-trials 20"
    echo ""
    
    echo "方案3: 内存存储（测试用）"
    echo "命令: python tools/optuna_trainer.py --storage memory --launcher pytorch --gpus \"0,1,2,3\" --n-trials 20"
    echo ""
    
else
    echo "单卡环境，可直接使用："
    echo "命令: python tools/optuna_trainer.py --storage sqlite:///work_dirs/optuna_studies.db --n-trials 20"
fi

# 检查修复文件是否存在
echo "🔍 检查修复文件..."
if [ -f "tools/optuna_storage_manager.py" ]; then
    echo "✅ 安全存储管理器已安装"
else
    echo "❌ 安全存储管理器缺失"
    echo "请确保 tools/optuna_storage_manager.py 文件存在"
fi

if [ -f "tools/optuna_multicard_fix.py" ]; then
    echo "✅ 多卡修复工具已安装"
else
    echo "❌ 多卡修复工具缺失"
fi

# 提供测试命令
echo ""
echo "🧪 测试命令:"
echo "============"
echo "1. 检查环境: python tools/optuna_multicard_fix.py --action check"
echo "2. 获取推荐: python tools/optuna_multicard_fix.py --action recommend"
echo "3. 测试存储: python tools/optuna_multicard_fix.py --action test --storage-type auto"

# 如果是多卡环境，提供额外建议
if [ "$MULTICARD" = true ]; then
    echo ""
    echo "⚠️  多卡环境注意事项:"
    echo "===================="
    echo "1. 确保所有GPU可用: nvidia-smi"
    echo "2. 使用正确的启动器: --launcher pytorch"
    echo "3. 指定GPU设备: --gpus \"0,1,2,3\""
    echo "4. 避免同时启动多个optuna进程"
    echo "5. 如果仍有问题，尝试Redis存储方案"
fi

echo ""
echo "✅ 修复脚本执行完成"
echo "如有问题，请查看 tools/optuna_multicard_fix.py 获取更多帮助"
